<?php
/**
 * P<PERSON><PERSON><PERSON> de <PERSON>
 * 
 * <AUTHOR> Agent
 * @version 1.0
 */

// DEBUG: Verificar se o arquivo está sendo executado
file_put_contents('debug_login.log', date('Y-m-d H:i:s') . " - Login.php carregado\n", FILE_APPEND);

require_once '../includes/config/config.php';

// Se já estiver logado, redirecionar
if (isLoggedIn()) {
    redirect('/account');
}

$errors = [];
$login_attempts_key = 'login_attempts_' . getRealIP();

// Verificar tentativas de login
if (isset($_SESSION[$login_attempts_key]) && $_SESSION[$login_attempts_key] >= MAX_LOGIN_ATTEMPTS) {
    $lockout_time = $_SESSION['lockout_time_' . getRealIP()] ?? 0;
    if (time() - $lockout_time < LOGIN_LOCKOUT_TIME) {
        $remaining = LOGIN_LOCKOUT_TIME - (time() - $lockout_time);
        $errors[] = "Muitas tentativas de login. Tente novamente em " . ceil($remaining / 60) . " minutos.";
    } else {
        // Reset tentativas após lockout
        unset($_SESSION[$login_attempts_key]);
        unset($_SESSION['lockout_time_' . getRealIP()]);
    }
}

// DEBUG: Log do método de requisição
file_put_contents('debug_login.log', date('Y-m-d H:i:s') . " - Método: " . $_SERVER['REQUEST_METHOD'] . "\n", FILE_APPEND);
file_put_contents('debug_login.log', date('Y-m-d H:i:s') . " - POST data: " . print_r($_POST, true) . "\n", FILE_APPEND);

// Processar formulário
if ($_SERVER['REQUEST_METHOD'] === 'POST' && empty($errors)) {
    file_put_contents('debug_login.log', date('Y-m-d H:i:s') . " - Processando formulário POST\n", FILE_APPEND);
    // Verificar CSRF
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Token de segurança inválido.';
    } else {
        // Sanitizar dados
        $username = sanitizeInput($_POST['username'] ?? '', 'username');
        $password = $_POST['password'] ?? '';
        $remember = isset($_POST['remember']);
        
        // Validações básicas
        if (empty($username)) {
            $errors[] = 'Nome de usuário é obrigatório.';
        }
        
        if (empty($password)) {
            $errors[] = 'Senha é obrigatória.';
        }
        
        // Tentar fazer login
        if (empty($errors)) {
            try {
                // DEBUG: Adicionar log de tentativa de conexão
                error_log("DEBUG: Tentando conectar ao banco auth");
                $auth_db = DatabaseManager::getConnection('auth');
                error_log("DEBUG: Conexão estabelecida com sucesso");
                
                // DEBUG: Log da busca de conta
                error_log("DEBUG: Buscando conta para usuário: " . $username);

                // Buscar conta
                $stmt = $auth_db->prepare("
                    SELECT id, username, sha_pass_hash, email, gmlevel, locked
                    FROM account
                    WHERE username = ?
                ");
                $stmt->execute([$username]);
                $account = $stmt->fetch();

                // DEBUG: Log do resultado
                error_log("DEBUG: Conta encontrada: " . ($account ? 'SIM' : 'NÃO'));
                
                if ($account) {
                    // Verificar se conta está bloqueada
                    if ($account['locked']) {
                        $errors[] = 'Conta bloqueada. Entre em contato com a administração.';
                    } else {
                        // DEBUG: Verificação de senha
                        $password_hash = strtoupper(sha1(strtoupper($username) . ':' . strtoupper($password)));
                        error_log("DEBUG: Hash calculado: " . $password_hash);
                        error_log("DEBUG: Hash no banco: " . $account['sha_pass_hash']);

                        if (hash_equals($account['sha_pass_hash'], $password_hash)) {
                            // Login bem-sucedido
                            session_regenerate_id(true);
                            
                            $_SESSION['user_id'] = $account['id'];
                            $_SESSION['username'] = $account['username'];
                            $_SESSION['email'] = $account['email'];
                            $_SESSION['user_level'] = $account['gmlevel'];
                            $_SESSION['login_time'] = time();
                            
                            // Reset tentativas de login
                            unset($_SESSION[$login_attempts_key]);
                            unset($_SESSION['lockout_time_' . getRealIP()]);
                            
                            // Atualizar último login
                            $stmt = $auth_db->prepare("
                                UPDATE account 
                                SET last_login = NOW(), last_ip = ? 
                                WHERE id = ?
                            ");
                            $stmt->execute([getRealIP(), $account['id']]);
                            
                            // Cookie "lembrar-me"
                            if ($remember) {
                                $token = bin2hex(random_bytes(32));
                                setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/', '', true, true);
                                // Salvar token no banco (implementar tabela remember_tokens se necessário)
                            }
                            
                            writeLog('INFO', 'User logged in', ['username' => $username, 'ip' => getRealIP()]);
                            
                            // Redirecionar
                            $redirect_to = $_GET['redirect'] ?? '/account';
                            redirect($redirect_to);
                            
                        } else {
                            $errors[] = 'Nome de usuário ou senha incorretos.';
                        }
                    }
                } else {
                    $errors[] = 'Nome de usuário ou senha incorretos.';
                }
                
            } catch (Exception $e) {
                // DEBUG: Log detalhado do erro
                error_log("DEBUG: Erro de login: " . $e->getMessage());
                error_log("DEBUG: Stack trace: " . $e->getTraceAsString());

                $errors[] = 'Erro ao fazer login. Tente novamente.';
                writeLog('ERROR', 'Login error: ' . $e->getMessage());
            }
        }
        
        // Incrementar tentativas de login em caso de erro
        if (!empty($errors)) {
            $_SESSION[$login_attempts_key] = ($_SESSION[$login_attempts_key] ?? 0) + 1;
            if ($_SESSION[$login_attempts_key] >= MAX_LOGIN_ATTEMPTS) {
                $_SESSION['lockout_time_' . getRealIP()] = time();
            }
        }
    }
}

// Dados para o template
$page_data = [
    'title' => 'Login - ' . SITE_NAME,
    'description' => 'Faça login no ' . SITE_NAME,
    'current_page' => 'login'
];

?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= escape($page_data['title']) ?></title>
    <meta name="description" content="<?= escape($page_data['description']) ?>">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?= ASSETS_PATH ?>/css/style.css" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= ASSETS_PATH ?>/images/favicon.ico">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-shield-alt me-2"></i>
                <?= SITE_NAME ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Início</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/download">Download</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/ranking">Ranking</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/forum">Fórum</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/donate">Doações</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link active" href="/login">Login</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/register">Registrar</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-5 pt-5">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white text-center">
                        <h4 class="mb-0">
                            <i class="fas fa-sign-in-alt"></i> Login
                        </h4>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle"></i>
                                <?php foreach ($errors as $error): ?>
                                    <div><?= escape($error) ?></div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" action="login.php<?= isset($_GET['redirect']) ? '?redirect=' . urlencode($_GET['redirect']) : '' ?>">
                            <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
                            
                            <div class="mb-3">
                                <label for="username" class="form-label">Nome de Usuário</label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="<?= escape($_POST['username'] ?? '') ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">Senha</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="remember" name="remember">
                                <label class="form-check-label" for="remember">
                                    Lembrar-me
                                </label>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-sign-in-alt"></i> Entrar
                                </button>
                            </div>
                        </form>
                        
                        <hr>
                        <div class="text-center">
                            <p class="mb-0">Não tem uma conta? <a href="/register">Registre-se</a></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
