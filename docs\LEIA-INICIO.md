# 🚨 REGRAS OBRIGATÓRIAS DO PROJETO - LEIA ANTES DE COMEÇAR

## ⚠️ REGRAS CRÍTICAS - NUNCA VIOLE ESTAS REGRAS

### 🔒 **PROIBIÇÕES ABSOLUTAS**

1. **❌ NUNCA ALTERE SEM PERMISSÃO:**
   - Layout do projeto
   - Estrutura de arquivos
   - Cores e design
   - Interface visual
   - Organização de pastas
   - Arquitetura do sistema

2. **❌ NUNCA FAÇA "JEITINHOS":**
   - Não simule que está funcionando
   - Não crie soluções temporárias
   - Não mascare problemas
   - Sempre resolva na raiz do problema

### ✅ **PROCEDIMENTOS OBRIGATÓRIOS**

#### 🔍 **ANTES DE COMEÇAR QUALQUER TRABALHO:**

1. **ANÁLISE COMPLETA DO PROJETO:**
   ```bash
   # Verificar estrutura atual
   - Entender arquitetura
   - Mapear dependências
   - Identificar componentes críticos
   - Documentar estado atual
   ```

2. **TESTES DE CONECTIVIDADE:**
   ```bash
   # Testar conexões SSH
   ssh user@servidor
   
   # Verificar acesso ao banco
   mysql -u user -p
   
   # Testar APIs
   curl -s http://localhost/api/stats.php
   ```

3. **SOLICITAR APROVAÇÃO:**
   - Explicar EXATAMENTE o que será alterado
   - Justificar a necessidade da mudança
   - Aguardar aprovação explícita
   - Documentar o plano de ação

#### 🛠️ **DURANTE O DESENVOLVIMENTO:**

1. **FOCO NO PROBLEMA REAL:**
   - Identificar causa raiz
   - Resolver o problema específico
   - Não criar soluções paralelas
   - Manter simplicidade

2. **NOMENCLATURA DE ARQUIVOS DE TESTE:**
   ```
   ✅ CORRETO:
   - test_conexao.php
   - debug_stats.js
   - temp_backup.sql
   - teste_api.html
   
   ❌ ERRADO:
   - arquivo.php
   - novo.js
   - backup.sql
   - index2.html
   ```

#### 🚀 **APÓS COMPLETAR ALTERAÇÕES:**

1. **TESTES OBRIGATÓRIOS:**
   ```bash
   # Testar funcionalidade alterada
   curl -s http://localhost/
   
   # Verificar APIs
   curl -s http://localhost/api/stats.php
   
   # Testar responsividade
   # Verificar logs de erro
   tail -f /var/log/nginx/error.log
   ```

2. **LIMPEZA DO PROJETO:**
   ```bash
   # Remover arquivos de teste
   rm test_*.php
   rm debug_*.js
   rm temp_*.sql
   rm teste_*.html
   ```

3. **DEPLOY OBRIGATÓRIO:**
   - Sempre fazer deploy após alterações
   - Verificar se deploy foi bem-sucedido
   - Testar em produção
   - Confirmar funcionamento

### 📋 **CHECKLIST OBRIGATÓRIO**

#### ✅ **ANTES DE INICIAR:**
- [ ] Analisei completamente o projeto atual
- [ ] Entendi a arquitetura e funcionamento
- [ ] Testei conexões SSH e banco de dados
- [ ] Identifiquei o problema real na raiz
- [ ] Expliquei o que será alterado
- [ ] Recebi aprovação explícita para mudanças

#### ✅ **DURANTE O TRABALHO:**
- [ ] Foquei apenas no problema específico
- [ ] Não alterei layout/design sem permissão
- [ ] Usei nomenclatura correta para arquivos de teste
- [ ] Resolvi o problema na raiz, não mascarei

#### ✅ **APÓS COMPLETAR:**
- [ ] Testei a funcionalidade corrigida
- [ ] Verifiquei se não quebrei outras funcionalidades
- [ ] Removi todos os arquivos de teste
- [ ] Fiz deploy das alterações
- [ ] Testei em produção
- [ ] Confirmei que está funcionando 100%

### 🚨 **CONSEQUÊNCIAS DE VIOLAÇÃO**

**Se violar estas regras:**
- ❌ Trabalho será rejeitado
- ❌ Alterações serão revertidas
- ❌ Projeto pode ser danificado
- ❌ Perda de confiança

### 💡 **PRINCÍPIOS FUNDAMENTAIS**

1. **TRANSPARÊNCIA TOTAL:**
   - Sempre explicar o que será feito
   - Documentar mudanças
   - Comunicar problemas encontrados

2. **QUALIDADE ACIMA DE VELOCIDADE:**
   - Fazer certo na primeira vez
   - Testar exaustivamente
   - Não deixar problemas para depois

3. **RESPEITO AO PROJETO:**
   - Manter integridade do código
   - Preservar arquitetura existente
   - Não fazer mudanças desnecessárias

---

## 📞 **EM CASO DE DÚVIDAS**

**SEMPRE PERGUNTE ANTES DE:**
- Alterar qualquer aspecto visual
- Modificar estrutura de arquivos
- Implementar nova funcionalidade
- Fazer mudanças na arquitetura

**LEMBRE-SE:** É melhor perguntar 10 vezes do que quebrar o projeto uma vez!

---

*Este documento deve ser lido e seguido rigorosamente por qualquer pessoa que trabalhe neste projeto.*
