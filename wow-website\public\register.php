<?php
/**
 * P<PERSON>gina de Registro
 * 
 * <AUTHOR> Agent
 * @version 1.0
 */

require_once '../includes/config/config.php';

// Se já estiver logado, redirecionar
if (isLoggedIn()) {
    redirect('/account');
}

$errors = [];
$success = false;

// Processar formulário
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verificar CSRF
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Token de segurança inválido.';
    } else {
        // Sanitizar dados
        $username = sanitizeInput($_POST['username'] ?? '', 'username');
        $email = sanitizeInput($_POST['email'] ?? '', 'email');
        $password = $_POST['password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        
        // Validações
        if (empty($username)) {
            $errors[] = 'Nome de usuário é obrigatório.';
        } elseif (strlen($username) < 3 || strlen($username) > 16) {
            $errors[] = 'Nome de usuário deve ter entre 3 e 16 caracteres.';
        } elseif (!preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
            $errors[] = 'Nome de usuário deve conter apenas letras, números e underscore.';
        }
        
        if (empty($email)) {
            $errors[] = 'Email é obrigatório.';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Email inválido.';
        }
        
        if (empty($password)) {
            $errors[] = 'Senha é obrigatória.';
        } elseif (strlen($password) < 6) {
            $errors[] = 'Senha deve ter pelo menos 6 caracteres.';
        }
        
        if ($password !== $confirm_password) {
            $errors[] = 'Senhas não coincidem.';
        }
        
        // Verificar se usuário já existe
        if (empty($errors)) {
            try {
                $auth_db = DatabaseManager::getConnection('auth');
                
                // Verificar username
                $stmt = $auth_db->prepare("SELECT id FROM account WHERE username = ?");
                $stmt->execute([$username]);
                if ($stmt->fetch()) {
                    $errors[] = 'Nome de usuário já existe.';
                }
                
                // Verificar email
                $stmt = $auth_db->prepare("SELECT id FROM account WHERE email = ?");
                $stmt->execute([$email]);
                if ($stmt->fetch()) {
                    $errors[] = 'Email já está em uso.';
                }
                
            } catch (Exception $e) {
                $errors[] = 'Erro ao verificar dados. Tente novamente.';
                writeLog('ERROR', 'Register check error: ' . $e->getMessage());
            }
        }
        
        // Criar conta se não há erros
        if (empty($errors)) {
            try {
                $auth_db = DatabaseManager::getConnection('auth');
                
                // Hash da senha (formato AzerothCore)
                $password_hash = strtoupper(sha1(strtoupper($username) . ':' . strtoupper($password)));
                
                // Inserir conta
                $stmt = $auth_db->prepare("
                    INSERT INTO account (username, sha_pass_hash, email, joindate, last_ip) 
                    VALUES (?, ?, ?, NOW(), ?)
                ");
                
                $client_ip = getRealIP();
                $stmt->execute([$username, $password_hash, $email, $client_ip]);
                
                $success = true;
                writeLog('INFO', 'New account created', ['username' => $username, 'email' => $email]);
                
            } catch (Exception $e) {
                $errors[] = 'Erro ao criar conta. Tente novamente.';
                writeLog('ERROR', 'Register creation error: ' . $e->getMessage());
            }
        }
    }
}

// Dados para o template
$page_data = [
    'title' => 'Registrar - ' . SITE_NAME,
    'description' => 'Crie sua conta no ' . SITE_NAME,
    'current_page' => 'register'
];

?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= escape($page_data['title']) ?></title>
    <meta name="description" content="<?= escape($page_data['description']) ?>">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?= ASSETS_PATH ?>/css/style.css" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= ASSETS_PATH ?>/images/favicon.ico">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-shield-alt me-2"></i>
                <?= SITE_NAME ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Início</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/download">Download</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/ranking">Ranking</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/forum">Fórum</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/donate">Doações</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/login">Login</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/register">Registrar</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-5 pt-5">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white text-center">
                        <h4 class="mb-0">
                            <i class="fas fa-user-plus"></i> Criar Conta
                        </h4>
                    </div>
                    <div class="card-body">
                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i>
                                <strong>Conta criada com sucesso!</strong><br>
                                Você já pode fazer <a href="/login" class="alert-link">login</a> e começar a jogar.
                            </div>
                        <?php else: ?>
                            <?php if (!empty($errors)): ?>
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <strong>Erro(s) encontrado(s):</strong>
                                    <ul class="mb-0 mt-2">
                                        <?php foreach ($errors as $error): ?>
                                            <li><?= escape($error) ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>
                            
                            <form method="POST" action="/register">
                                <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
                                
                                <div class="mb-3">
                                    <label for="username" class="form-label">Nome de Usuário</label>
                                    <input type="text" class="form-control" id="username" name="username" 
                                           value="<?= escape($_POST['username'] ?? '') ?>" 
                                           required maxlength="16" pattern="[a-zA-Z0-9_]+">
                                    <div class="form-text">3-16 caracteres, apenas letras, números e underscore</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?= escape($_POST['email'] ?? '') ?>" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="password" class="form-label">Senha</label>
                                    <input type="password" class="form-control" id="password" name="password" 
                                           required minlength="6">
                                    <div class="form-text">Mínimo 6 caracteres</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label">Confirmar Senha</label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                </div>
                                
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-user-plus"></i> Criar Conta
                                    </button>
                                </div>
                            </form>
                        <?php endif; ?>
                        
                        <hr>
                        <div class="text-center">
                            <p class="mb-0">Já tem uma conta? <a href="/login">Faça login</a></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Validação do formulário -->
    <script>
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            
            if (password !== confirmPassword) {
                this.setCustomValidity('Senhas não coincidem');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
