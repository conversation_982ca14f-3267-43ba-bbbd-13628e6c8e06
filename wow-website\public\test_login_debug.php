<?php
/**
 * Teste de Login - Diagnóstico Comple<PERSON>
 * 
 * <AUTHOR> Agent
 * @version 1.0
 */

require_once '../includes/config/config.php';

// Credenciais de teste
$test_username = 'cavalcrod20256';
$test_password = '200381';

echo "<h2>🔍 Teste de Login - Diagnóstico Completo</h2>\n";
echo "<p><strong>Usuário de teste:</strong> " . htmlspecialchars($test_username) . "</p>\n";
echo "<p><strong>Senha:</strong> [OCULTA POR SEGURANÇA]</p>\n";
echo "<hr>\n";

try {
    // Conectar ao banco auth
    $auth_db = DatabaseManager::getConnection('auth');
    echo "<p>✅ Conexão com banco estabelecida</p>\n";
    
    // 1. Verificar se a conta existe
    echo "<h3>1️⃣ Verificando se a conta existe:</h3>\n";
    $stmt = $auth_db->prepare("SELECT id, username, sha_pass_hash, salt, verifier, email, locked FROM account WHERE username = ?");
    $stmt->execute([$test_username]);
    $account = $stmt->fetch();
    
    if (!$account) {
        echo "<p style='color: red;'>❌ Conta não encontrada no banco de dados!</p>\n";
        echo "<p>Verifique se:</p>\n";
        echo "<ul>\n";
        echo "<li>O nome de usuário está correto</li>\n";
        echo "<li>A conta foi criada no banco correto</li>\n";
        echo "<li>Não há problemas de case-sensitive</li>\n";
        echo "</ul>\n";
        exit;
    }
    
    echo "<p>✅ Conta encontrada!</p>\n";
    echo "<ul>\n";
    echo "<li><strong>ID:</strong> " . htmlspecialchars($account['id']) . "</li>\n";
    echo "<li><strong>Username:</strong> " . htmlspecialchars($account['username']) . "</li>\n";
    echo "<li><strong>Email:</strong> " . htmlspecialchars($account['email']) . "</li>\n";
    echo "<li><strong>Locked:</strong> " . ($account['locked'] ? 'SIM' : 'NÃO') . "</li>\n";
    echo "</ul>\n";
    
    // 2. Verificar campos de autenticação disponíveis
    echo "<h3>2️⃣ Campos de autenticação disponíveis:</h3>\n";
    $has_sha_pass_hash = !empty($account['sha_pass_hash']);
    $has_salt = !empty($account['salt']);
    $has_verifier = !empty($account['verifier']);
    
    echo "<ul>\n";
    echo "<li><strong>sha_pass_hash:</strong> " . ($has_sha_pass_hash ? '✅ PRESENTE' : '❌ VAZIO') . "</li>\n";
    echo "<li><strong>salt:</strong> " . ($has_salt ? '✅ PRESENTE' : '❌ VAZIO') . "</li>\n";
    echo "<li><strong>verifier:</strong> " . ($has_verifier ? '✅ PRESENTE' : '❌ VAZIO') . "</li>\n";
    echo "</ul>\n";
    
    // 3. Teste de autenticação SHA1 (formato clássico)
    echo "<h3>3️⃣ Teste SHA1 (formato clássico AzerothCore):</h3>\n";
    if ($has_sha_pass_hash) {
        $password_hash_v1 = strtoupper(sha1(strtoupper($test_username) . ':' . strtoupper($test_password)));
        $password_hash_v2 = sha1(strtoupper($test_username . ':' . $test_password));
        $password_hash_v3 = strtoupper(sha1($test_username . ':' . $test_password));
        
        echo "<p><strong>Hash no banco:</strong> " . htmlspecialchars($account['sha_pass_hash']) . "</p>\n";
        echo "<p><strong>Hash calculado v1:</strong> " . htmlspecialchars($password_hash_v1) . "</p>\n";
        echo "<p><strong>Hash calculado v2:</strong> " . htmlspecialchars($password_hash_v2) . "</p>\n";
        echo "<p><strong>Hash calculado v3:</strong> " . htmlspecialchars($password_hash_v3) . "</p>\n";
        
        if (hash_equals($account['sha_pass_hash'], $password_hash_v1)) {
            echo "<p style='color: green;'>✅ SUCESSO: SHA1 v1 (uppercase user:pass)</p>\n";
            $auth_method = 'sha1_v1';
        } elseif (hash_equals($account['sha_pass_hash'], $password_hash_v2)) {
            echo "<p style='color: green;'>✅ SUCESSO: SHA1 v2 (uppercase user:pass, lowercase hash)</p>\n";
            $auth_method = 'sha1_v2';
        } elseif (hash_equals($account['sha_pass_hash'], $password_hash_v3)) {
            echo "<p style='color: green;'>✅ SUCESSO: SHA1 v3 (normal user:pass, uppercase hash)</p>\n";
            $auth_method = 'sha1_v3';
        } else {
            echo "<p style='color: red;'>❌ FALHA: Nenhuma variação SHA1 funcionou</p>\n";
            $auth_method = null;
        }
    } else {
        echo "<p style='color: orange;'>⚠️ Campo sha_pass_hash está vazio</p>\n";
    }
    
    // 4. Teste de autenticação SRP6 (formato moderno)
    echo "<h3>4️⃣ Teste SRP6 (formato moderno):</h3>\n";
    if ($has_salt && $has_verifier) {
        echo "<p>⚠️ SRP6 detectado, mas implementação complexa</p>\n";
        echo "<p>Salt presente: " . ($has_salt ? 'SIM' : 'NÃO') . "</p>\n";
        echo "<p>Verifier presente: " . ($has_verifier ? 'SIM' : 'NÃO') . "</p>\n";
    } else {
        echo "<p style='color: orange;'>⚠️ Campos salt/verifier não estão disponíveis</p>\n";
    }
    
    // 5. Resultado final
    echo "<h3>5️⃣ Resultado do Diagnóstico:</h3>\n";
    if (isset($auth_method) && $auth_method) {
        echo "<p style='color: green; font-weight: bold;'>✅ MÉTODO DE AUTENTICAÇÃO IDENTIFICADO: " . strtoupper($auth_method) . "</p>\n";
        echo "<p>O login deve funcionar com este método.</p>\n";
        
        // Teste de login real
        echo "<h4>🧪 Teste de Login Real:</h4>\n";
        try {
            session_start();
            
            // Simular login bem-sucedido
            $_SESSION['user_id'] = $account['id'];
            $_SESSION['username'] = $account['username'];
            $_SESSION['email'] = $account['email'];
            $_SESSION['login_time'] = time();
            
            echo "<p style='color: green;'>✅ Sessão criada com sucesso!</p>\n";
            echo "<p>Dados da sessão:</p>\n";
            echo "<ul>\n";
            echo "<li>User ID: " . $_SESSION['user_id'] . "</li>\n";
            echo "<li>Username: " . $_SESSION['username'] . "</li>\n";
            echo "<li>Email: " . $_SESSION['email'] . "</li>\n";
            echo "</ul>\n";
            
            // Limpar sessão de teste
            session_unset();
            session_destroy();
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Erro ao criar sessão: " . htmlspecialchars($e->getMessage()) . "</p>\n";
        }
        
    } else {
        echo "<p style='color: red; font-weight: bold;'>❌ NENHUM MÉTODO DE AUTENTICAÇÃO FUNCIONOU</p>\n";
        echo "<p>Possíveis problemas:</p>\n";
        echo "<ul>\n";
        echo "<li>Senha incorreta</li>\n";
        echo "<li>Formato de hash não suportado</li>\n";
        echo "<li>Conta criada com método diferente</li>\n";
        echo "</ul>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Erro durante o teste: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<hr>\n";
echo "<p><a href='login.php'>← Voltar para Login</a> | <a href='test_db_structure.php'>Ver Estrutura do Banco</a></p>\n";
?>
