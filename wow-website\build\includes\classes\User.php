<?php
/**
 * Classe User - Gerenciamento de Usuários
 * 
 * <AUTHOR> Agent
 * @version 1.0
 */

class User {
    private $db;
    private $user_data;
    
    public function __construct() {
        $this->db = DatabaseManager::getConnection('auth');
    }
    
    /**
     * Autenticar usuário
     */
    public function authenticate($username, $password) {
        try {
            // Verificar tentativas de login
            $ip = getRealIP();
            if (!checkLoginAttempts($username, $ip)) {
                throw new Exception('Muitas tentativas de login. Tente novamente em ' . (LOGIN_LOCKOUT_TIME / 60) . ' minutos.');
            }
            
            // Buscar usuário no banco
            $stmt = $this->db->prepare("
                SELECT id, username, sha_pass_hash, email, joindate, last_login, 
                       expansion, gmlevel, locked, online
                FROM account 
                WHERE username = ? AND locked = 0
            ");
            $stmt->execute([$username]);
            $user = $stmt->fetch();
            
            if (!$user) {
                recordFailedLogin($username, $ip);
                throw new Exception('Usuário ou senha incorretos');
            }
            
            // Verificar senha (AzerothCore usa SHA1 do username:password em uppercase)
            $expected_hash = sha1(strtoupper($username . ':' . $password));
            
            if (!hash_equals($user['sha_pass_hash'], $expected_hash)) {
                recordFailedLogin($username, $ip);
                throw new Exception('Usuário ou senha incorretos');
            }
            
            // Login bem-sucedido
            clearLoginAttempts($username, $ip);
            $this->user_data = $user;
            
            // Atualizar último login
            $this->updateLastLogin($user['id']);
            
            // Criar sessão
            $this->createSession();
            
            writeLog('INFO', 'User logged in successfully', [
                'username' => $username,
                'ip' => $ip,
                'user_id' => $user['id']
            ]);
            
            return true;
            
        } catch (Exception $e) {
            writeLog('WARNING', 'Login failed: ' . $e->getMessage(), [
                'username' => $username,
                'ip' => getRealIP()
            ]);
            throw $e;
        }
    }
    
    /**
     * Registrar novo usuário
     */
    public function register($username, $password, $email) {
        try {
            // Validações
            $this->validateRegistration($username, $password, $email);
            
            // Verificar se usuário já existe
            if ($this->userExists($username, $email)) {
                throw new Exception('Usuário ou email já cadastrado');
            }
            
            // Criar hash da senha
            $password_hash = sha1(strtoupper($username . ':' . $password));
            
            // Inserir no banco
            $stmt = $this->db->prepare("
                INSERT INTO account (username, sha_pass_hash, email, joindate, expansion) 
                VALUES (?, ?, ?, NOW(), ?)
            ");
            
            $expansion = 2; // Wrath of the Lich King
            $stmt->execute([$username, $password_hash, $email, $expansion]);
            
            $user_id = $this->db->lastInsertId();
            
            writeLog('INFO', 'New user registered', [
                'username' => $username,
                'email' => $email,
                'user_id' => $user_id,
                'ip' => getRealIP()
            ]);
            
            return $user_id;
            
        } catch (Exception $e) {
            writeLog('ERROR', 'Registration failed: ' . $e->getMessage(), [
                'username' => $username,
                'email' => $email,
                'ip' => getRealIP()
            ]);
            throw $e;
        }
    }
    
    /**
     * Validar dados de registro
     */
    private function validateRegistration($username, $password, $email) {
        $errors = [];
        
        // Username
        if (strlen($username) < 3 || strlen($username) > 16) {
            $errors[] = 'Username deve ter entre 3 e 16 caracteres';
        }
        
        if (!preg_match('/^[a-zA-Z0-9]+$/', $username)) {
            $errors[] = 'Username deve conter apenas letras e números';
        }
        
        // Password
        if (strlen($password) < 6 || strlen($password) > 16) {
            $errors[] = 'Senha deve ter entre 6 e 16 caracteres';
        }
        
        // Email
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Email inválido';
        }
        
        if (!empty($errors)) {
            throw new Exception(implode(', ', $errors));
        }
    }
    
    /**
     * Verificar se usuário existe
     */
    private function userExists($username, $email) {
        $stmt = $this->db->prepare("
            SELECT COUNT(*) FROM account 
            WHERE username = ? OR email = ?
        ");
        $stmt->execute([$username, $email]);
        
        return $stmt->fetchColumn() > 0;
    }
    
    /**
     * Atualizar último login
     */
    private function updateLastLogin($user_id) {
        $stmt = $this->db->prepare("
            UPDATE account 
            SET last_login = NOW(), last_ip = ? 
            WHERE id = ?
        ");
        $stmt->execute([getRealIP(), $user_id]);
    }
    
    /**
     * Criar sessão do usuário
     */
    private function createSession() {
        $_SESSION['user_id'] = $this->user_data['id'];
        $_SESSION['username'] = $this->user_data['username'];
        $_SESSION['email'] = $this->user_data['email'];
        $_SESSION['user_level'] = $this->user_data['gmlevel'];
        $_SESSION['login_time'] = time();
        $_SESSION['expansion'] = $this->user_data['expansion'];
        
        // Regenerar ID da sessão por segurança
        session_regenerate_id(true);
    }
    
    /**
     * Obter dados do usuário atual
     */
    public static function getCurrentUser() {
        if (!isLoggedIn()) {
            return null;
        }
        
        return [
            'id' => $_SESSION['user_id'],
            'username' => $_SESSION['username'],
            'email' => $_SESSION['email'],
            'level' => $_SESSION['user_level'],
            'expansion' => $_SESSION['expansion']
        ];
    }
    
    /**
     * Obter personagens do usuário
     */
    public function getCharacters($user_id = null) {
        $user_id = $user_id ?: $_SESSION['user_id'];
        
        try {
            $char_db = DatabaseManager::getConnection('characters');
            
            $stmt = $char_db->prepare("
                SELECT guid, name, race, class, gender, level, zone, 
                       map, totaltime, leveltime, logout_time, online
                FROM characters 
                WHERE account = ? 
                ORDER BY level DESC, totaltime DESC
            ");
            $stmt->execute([$user_id]);
            
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            writeLog('ERROR', 'Failed to get user characters: ' . $e->getMessage(), [
                'user_id' => $user_id
            ]);
            return [];
        }
    }
    
    /**
     * Alterar senha
     */
    public function changePassword($current_password, $new_password) {
        try {
            $user_id = $_SESSION['user_id'];
            $username = $_SESSION['username'];
            
            // Verificar senha atual
            $stmt = $this->db->prepare("SELECT sha_pass_hash FROM account WHERE id = ?");
            $stmt->execute([$user_id]);
            $current_hash = $stmt->fetchColumn();
            
            $expected_hash = sha1(strtoupper($username . ':' . $current_password));
            
            if (!hash_equals($current_hash, $expected_hash)) {
                throw new Exception('Senha atual incorreta');
            }
            
            // Validar nova senha
            if (strlen($new_password) < 6 || strlen($new_password) > 16) {
                throw new Exception('Nova senha deve ter entre 6 e 16 caracteres');
            }
            
            // Atualizar senha
            $new_hash = sha1(strtoupper($username . ':' . $new_password));
            
            $stmt = $this->db->prepare("UPDATE account SET sha_pass_hash = ? WHERE id = ?");
            $stmt->execute([$new_hash, $user_id]);
            
            writeLog('INFO', 'User changed password', [
                'username' => $username,
                'user_id' => $user_id,
                'ip' => getRealIP()
            ]);
            
            return true;
            
        } catch (Exception $e) {
            writeLog('ERROR', 'Password change failed: ' . $e->getMessage(), [
                'user_id' => $_SESSION['user_id'] ?? 'unknown',
                'ip' => getRealIP()
            ]);
            throw $e;
        }
    }
    
    /**
     * Obter estatísticas do usuário
     */
    public function getUserStats($user_id = null) {
        $user_id = $user_id ?: $_SESSION['user_id'];
        
        try {
            $char_db = DatabaseManager::getConnection('characters');
            
            // Total de personagens
            $stmt = $char_db->prepare("SELECT COUNT(*) FROM characters WHERE account = ?");
            $stmt->execute([$user_id]);
            $total_chars = $stmt->fetchColumn();
            
            // Personagem de maior nível
            $stmt = $char_db->prepare("
                SELECT name, level, class FROM characters 
                WHERE account = ? 
                ORDER BY level DESC 
                LIMIT 1
            ");
            $stmt->execute([$user_id]);
            $highest_char = $stmt->fetch();
            
            // Tempo total jogado
            $stmt = $char_db->prepare("SELECT SUM(totaltime) FROM characters WHERE account = ?");
            $stmt->execute([$user_id]);
            $total_time = $stmt->fetchColumn() ?: 0;
            
            return [
                'total_characters' => $total_chars,
                'highest_character' => $highest_char,
                'total_playtime' => $total_time,
                'total_playtime_formatted' => $this->formatPlaytime($total_time)
            ];
            
        } catch (Exception $e) {
            writeLog('ERROR', 'Failed to get user stats: ' . $e->getMessage(), [
                'user_id' => $user_id
            ]);
            return [
                'total_characters' => 0,
                'highest_character' => null,
                'total_playtime' => 0,
                'total_playtime_formatted' => '0h 0m'
            ];
        }
    }
    
    /**
     * Formatar tempo de jogo
     */
    private function formatPlaytime($seconds) {
        $days = floor($seconds / 86400);
        $hours = floor(($seconds % 86400) / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        
        $parts = [];
        if ($days > 0) $parts[] = $days . 'd';
        if ($hours > 0) $parts[] = $hours . 'h';
        if ($minutes > 0) $parts[] = $minutes . 'm';
        
        return !empty($parts) ? implode(' ', $parts) : '0m';
    }
    
    /**
     * Verificar se usuário é GM
     */
    public function isGameMaster($user_id = null) {
        $user_id = $user_id ?: $_SESSION['user_id'];
        
        try {
            $stmt = $this->db->prepare("SELECT gmlevel FROM account WHERE id = ?");
            $stmt->execute([$user_id]);
            $gm_level = $stmt->fetchColumn();
            
            return $gm_level >= 1;
            
        } catch (Exception $e) {
            return false;
        }
    }
}
?>
