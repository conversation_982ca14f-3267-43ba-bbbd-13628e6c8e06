/**
 * Estilos Principais do Site WoW
 * 
 * <AUTHOR> Agent
 * @version 1.0
 */

/* ===== VARIÁVEIS CSS ===== */
:root {
    --primary-color: #f4c430;
    --secondary-color: #1a1a2e;
    --accent-color: #16213e;
    --text-light: #ffffff;
    --text-dark: #333333;
    --alliance-color: #0078ff;
    --horde-color: #b30000;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
}

/* ===== RESET E BASE ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: #f8f9fa;
}

/* ===== NAVEGAÇÃO ===== */
.navbar {
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color)) !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
    color: var(--primary-color) !important;
}

.navbar-brand img {
    margin-right: 10px;
}

.navbar-nav .nav-link {
    color: var(--text-light) !important;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--primary-color) !important;
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 50%;
    background-color: var(--primary-color);
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover::after,
.navbar-nav .nav-link.active::after {
    width: 100%;
    left: 0;
}

/* ===== HERO SECTION ===== */
.hero-section {
    background: linear-gradient(rgba(0,0,0,0.6), rgba(0,0,0,0.6)), 
                url('../images/hero-bg.jpg') center/cover no-repeat;
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    padding-top: 80px;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(26,26,46,0.8), rgba(22,33,62,0.8));
}

.hero-content {
    position: relative;
    z-index: 2;
    animation: fadeInUp 1s ease-out;
}

.hero-buttons .btn {
    margin: 5px;
    padding: 12px 30px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.hero-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

/* ===== SERVER STATUS CARD ===== */
.server-status-card {
    animation: fadeInRight 1s ease-out 0.3s both;
}

.server-status-card .card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    backdrop-filter: blur(10px);
    background: rgba(26,26,46,0.9) !important;
}

.stat-item {
    padding: 15px 0;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    color: #adb5bd;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* ===== SECTIONS ===== */
.section-title {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--secondary-color);
    margin-bottom: 15px;
}

.section-subtitle {
    font-size: 1.2rem;
    color: #6c757d;
    margin-bottom: 30px;
}

/* ===== FEATURES SECTION ===== */
.features-section {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.feature-card {
    padding: 30px 20px;
    border-radius: 15px;
    background: white;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.2);
}

.feature-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.feature-card h4 {
    color: var(--secondary-color);
    margin-bottom: 15px;
    font-weight: 600;
}

/* ===== TOP PLAYERS SECTION ===== */
.top-players-section {
    background: linear-gradient(135deg, #e9ecef, #dee2e6);
}

.rank-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 15px;
    font-weight: bold;
    font-size: 0.9rem;
}

.rank-1 {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #333;
}

.rank-2 {
    background: linear-gradient(45deg, #c0c0c0, #e5e5e5);
    color: #333;
}

.rank-3 {
    background: linear-gradient(45deg, #cd7f32, #daa520);
    color: white;
}

.rank-4,
.rank-5 {
    background: linear-gradient(45deg, var(--info-color), #20c997);
    color: white;
}

/* ===== FACTION STYLES ===== */
.faction-alliance {
    color: var(--alliance-color);
    font-weight: 600;
}

.faction-horde {
    color: var(--horde-color);
    font-weight: 600;
}

/* ===== FOOTER ===== */
.footer {
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color)) !important;
    border-top: 3px solid var(--primary-color);
}

.social-links a {
    font-size: 1.5rem;
    transition: color 0.3s ease;
}

.social-links a:hover {
    color: var(--primary-color) !important;
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .hero-section {
        text-align: center;
        padding: 100px 0 50px;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .hero-content h1 {
        font-size: 2.5rem;
    }
    
    .server-status-card {
        margin-top: 30px;
    }
    
    .feature-card {
        margin-bottom: 20px;
    }
}

@media (max-width: 576px) {
    .hero-content h1 {
        font-size: 2rem;
    }
    
    .hero-buttons .btn {
        display: block;
        width: 100%;
        margin: 10px 0;
    }
    
    .stat-value {
        font-size: 1.2rem;
    }
}

/* ===== UTILITY CLASSES ===== */
.text-primary-custom {
    color: var(--primary-color) !important;
}

.bg-primary-custom {
    background-color: var(--primary-color) !important;
}

.btn-primary-custom {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--text-dark);
    font-weight: 600;
}

.btn-primary-custom:hover {
    background-color: #e6b82a;
    border-color: #e6b82a;
    color: var(--text-dark);
}

/* ===== LOADING SPINNER ===== */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* ===== CUSTOM SCROLLBAR ===== */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #e6b82a;
}

/* ===== TOOLTIPS ===== */
.tooltip-inner {
    background-color: var(--secondary-color);
    color: var(--text-light);
}

.tooltip.bs-tooltip-top .tooltip-arrow::before {
    border-top-color: var(--secondary-color);
}

/* ===== CARDS ===== */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), #e6b82a);
    color: var(--text-dark);
    font-weight: 600;
    border-radius: 15px 15px 0 0 !important;
}

/* ===== BUTTONS ===== */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

/* ===== TABLES ===== */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    background-color: var(--secondary-color);
    color: var(--text-light);
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.9rem;
}

.table tbody tr:hover {
    background-color: rgba(244, 196, 48, 0.1);
}

/* ===== FORMS ===== */
.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(244, 196, 48, 0.25);
}
