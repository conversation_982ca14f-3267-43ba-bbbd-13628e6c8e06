<?php
/**
 * <PERSON><PERSON><PERSON><PERSON>ut
 * 
 * <AUTHOR> Agent
 * @version 1.0
 */

require_once '../includes/config/config.php';

// Log do logout
if (isLoggedIn()) {
    writeLog('INFO', 'User logged out', [
        'username' => $_SESSION['username'] ?? 'unknown',
        'ip' => getRealIP()
    ]);
}

// Limpar sessão
session_destroy();

// Limpar cookies
if (isset($_COOKIE['remember_token'])) {
    setcookie('remember_token', '', time() - 3600, '/', '', true, true);
}

// Redirecionar para home
redirect('/');
?>
