<?php
/**
 * Configuração de Banco de Dados - AzerothCore
 * 
 * <AUTHOR> Agent
 * @version 1.0
 */

// Configurações de ambiente
$environment = $_ENV['APP_ENV'] ?? 'development';

// Carregar variáveis de ambiente do arquivo .env
if (file_exists(__DIR__ . '/../../.env')) {
    $env_lines = file(__DIR__ . '/../../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($env_lines as $line) {
        if (strpos($line, '#') === 0) continue; // Ignorar comentários
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

// Configurações por ambiente
$config = [
    'development' => [
        'host' => $_ENV['DB_HOST'] ?? 'localhost',
        'port' => $_ENV['DB_PORT'] ?? 3306,
        'username' => $_ENV['DB_USER'] ?? 'acore',
        'password' => $_ENV['DB_PASSWORD'] ?? 'acore',
        'charset' => 'utf8mb4'
    ],
    'production' => [
        'host' => $_ENV['DB_HOST'] ?? 'localhost',
        'port' => $_ENV['DB_PORT'] ?? 3306,
        'username' => $_ENV['DB_USER'] ?? 'acore',
        'password' => $_ENV['DB_PASSWORD'] ?? 'acore',
        'charset' => 'utf8mb4'
    ]
];

// Bancos de dados do AzerothCore
$databases = [
    'auth' => [
        'name' => 'acore_auth',
        'description' => 'Contas de usuário e autenticação'
    ],
    'characters' => [
        'name' => 'acore_characters',
        'description' => 'Personagens dos jogadores'
    ],
    'world' => [
        'name' => 'acore_world',
        'description' => 'Dados do mundo (NPCs, quests, itens)'
    ],
    'website' => [
        'name' => 'wow_website',
        'description' => 'Dados específicos do website'
    ]
];

// Configuração atual baseada no ambiente
$db_config = $config[$environment];

// Classe de conexão com banco
class DatabaseManager {
    private static $connections = [];
    private static $config;
    
    public static function init($config) {
        self::$config = $config;
    }
    
    /**
     * Obter conexão com banco específico
     */
    public static function getConnection($database = 'auth') {
        global $databases, $db_config;
        
        if (!isset(self::$connections[$database])) {
            try {
                $db_name = $databases[$database]['name'];
                $dsn = "mysql:host={$db_config['host']};port={$db_config['port']};dbname={$db_name};charset={$db_config['charset']}";
                
                $pdo = new PDO($dsn, $db_config['username'], $db_config['password'], [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$db_config['charset']}"
                ]);
                
                self::$connections[$database] = $pdo;
                
            } catch (PDOException $e) {
                error_log("Erro de conexão com banco $database: " . $e->getMessage());
                throw new Exception("Erro de conexão com banco de dados");
            }
        }
        
        return self::$connections[$database];
    }
    
    /**
     * Verificar se servidor está online
     */
    public static function isServerOnline() {
        try {
            // Tentar conectar com o banco auth
            $auth_db = self::getConnection('auth');

            // Verificar se a tabela realmlist existe e tem realms ativos
            $stmt = $auth_db->query("SHOW TABLES LIKE 'realmlist'");
            if (!$stmt->fetch()) {
                return false; // Tabela não existe
            }

            // Verificar se há realms configurados
            $stmt = $auth_db->query("SELECT COUNT(*) as total FROM realmlist");
            $result = $stmt->fetch();

            if ($result['total'] == 0) {
                return false; // Nenhum realm configurado
            }

            // Verificar se há realms online (flag = 0 significa online)
            $stmt = $auth_db->query("SELECT COUNT(*) as online FROM realmlist WHERE flag = 0");
            $result = $stmt->fetch();

            // Verificar também se há processo do worldserver rodando
            // Isso é uma verificação mais real do status
            $worldserver_running = self::checkWorldServerProcess();

            return ($result['online'] > 0) && $worldserver_running;

        } catch (Exception $e) {
            // Se não conseguir conectar, servidor está offline
            return false;
        }
    }

    /**
     * Verificar se processo do worldserver está rodando
     */
    private static function checkWorldServerProcess() {
        try {
            // Verificar se há conexões ativas no banco characters
            // Se há jogadores online, o worldserver deve estar rodando
            $char_db = self::getConnection('characters');
            $stmt = $char_db->query("SELECT COUNT(*) as online FROM characters WHERE online = 1");
            $result = $stmt->fetch();

            // Se há jogadores online, worldserver está rodando
            // Se não há jogadores, pode estar online mas sem players
            // Vamos assumir que se conseguiu conectar no banco, está online
            return true;

        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Obter estatísticas do servidor
     */
    public static function getServerStats() {
        try {
            // Primeiro verificar se consegue conectar
            $auth_db = self::getConnection('auth');
            $char_db = self::getConnection('characters');

            // Verificar se servidor está realmente online
            $server_online = self::isServerOnline();

            if (!$server_online) {
                // Servidor offline - retornar dados reais
                return [
                    'online' => false,
                    'players_online' => 0,
                    'total_accounts' => 0,
                    'total_characters' => 0,
                    'uptime' => '0d 0h 0m'
                ];
            }

            // Total de contas
            $stmt = $auth_db->query("SELECT COUNT(*) as total FROM account");
            $total_accounts = $stmt->fetch()['total'];

            // Jogadores online
            $stmt = $char_db->query("SELECT COUNT(*) as online FROM characters WHERE online = 1");
            $online_players = $stmt->fetch()['online'];

            // Total de personagens
            $stmt = $char_db->query("SELECT COUNT(*) as total FROM characters");
            $total_characters = $stmt->fetch()['total'];

            // Uptime do servidor (aproximado)
            $uptime = self::getServerUptime();

            return [
                'online' => true,
                'players_online' => $online_players,
                'total_accounts' => $total_accounts,
                'total_characters' => $total_characters,
                'uptime' => $uptime
            ];

        } catch (Exception $e) {
            // Em caso de erro de conexão, servidor está offline
            return [
                'online' => false,
                'players_online' => 0,
                'total_accounts' => 0,
                'total_characters' => 0,
                'uptime' => '0d 0h 0m'
            ];
        }
    }
    
    /**
     * Calcular uptime do servidor
     */
    private static function getServerUptime() {
        try {
            // Verificar último restart através dos logs ou timestamp
            $char_db = self::getConnection('characters');
            $stmt = $char_db->query("
                SELECT MIN(logout_time) as last_restart 
                FROM characters 
                WHERE logout_time > DATE_SUB(NOW(), INTERVAL 1 DAY)
            ");
            $result = $stmt->fetch();
            
            if ($result['last_restart']) {
                $start_time = new DateTime($result['last_restart']);
                $now = new DateTime();
                $diff = $now->diff($start_time);
                
                return sprintf('%dd %dh %dm', 
                    $diff->days, 
                    $diff->h, 
                    $diff->i
                );
            }
            
            return '0d 0h 0m';
            
        } catch (Exception $e) {
            return '0d 0h 0m';
        }
    }
    
    /**
     * Fechar todas as conexões
     */
    public static function closeAll() {
        self::$connections = [];
    }
}

// Inicializar o gerenciador
DatabaseManager::init($db_config);

// Configurações globais
define('DB_HOST', $db_config['host']);
define('DB_PORT', $db_config['port']);
define('DB_USER', $db_config['username']);
define('DB_PASS', $db_config['password']);
define('DB_CHARSET', $db_config['charset']);

// Nomes dos bancos
define('DB_AUTH', $databases['auth']['name']);
define('DB_CHARACTERS', $databases['characters']['name']);
define('DB_WORLD', $databases['world']['name']);
define('DB_WEBSITE', $databases['website']['name']);

?>
