<?php
/**
 * P<PERSON>gina de Registro
 * 
 * <AUTHOR> Agent
 * @version 1.0
 */

require_once '../includes/config/config.php';

// Se já estiver logado, redirecionar
if (isLoggedIn()) {
    redirect('/account');
}

$errors = [];
$success = false;

// Processar formulário
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verificar CSRF
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Token de segurança inválido.';
    } else {
        // Sanitizar dados
        $username = sanitizeInput($_POST['username'] ?? '', 'username');
        $email = sanitizeInput($_POST['email'] ?? '', 'email');
        $password = $_POST['password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        
        // Validações
        if (empty($username)) {
            $errors[] = 'Nome de usuário é obrigatório.';
        } elseif (strlen($username) < 3 || strlen($username) > 16) {
            $errors[] = 'Nome de usuário deve ter entre 3 e 16 caracteres.';
        } elseif (!preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
            $errors[] = 'Nome de usuário deve conter apenas letras, números e underscore.';
        }
        
        if (empty($email)) {
            $errors[] = 'Email é obrigatório.';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Email inválido.';
        }
        
        if (empty($password)) {
            $errors[] = 'Senha é obrigatória.';
        } elseif (strlen($password) < 6) {
            $errors[] = 'Senha deve ter pelo menos 6 caracteres.';
        }
        
        if ($password !== $confirm_password) {
            $errors[] = 'Senhas não coincidem.';
        }
        
        // Verificar se usuário já existe
        if (empty($errors)) {
            try {
                $auth_db = DatabaseManager::getConnection('auth');
                
                // Verificar username
                $stmt = $auth_db->prepare("SELECT id FROM account WHERE username = ?");
                $stmt->execute([$username]);
                if ($stmt->fetch()) {
                    $errors[] = 'Nome de usuário já existe.';
                }
                
                // Verificar email
                $stmt = $auth_db->prepare("SELECT id FROM account WHERE email = ?");
                $stmt->execute([$email]);
                if ($stmt->fetch()) {
                    $errors[] = 'Email já está em uso.';
                }
                
            } catch (Exception $e) {
                $errors[] = 'Erro ao verificar dados. Tente novamente.';
                writeLog('ERROR', 'Register check error: ' . $e->getMessage());
            }
        }
        
        // Criar conta se não há erros
        if (empty($errors)) {
            try {
                $auth_db = DatabaseManager::getConnection('auth');
                
                // Gerar salt e verifier (formato AzerothCore moderno)
                $salt = random_bytes(32);
                $verifier = calculateSRP6Verifier($username, $password, $salt);

                // Inserir conta
                $stmt = $auth_db->prepare("
                    INSERT INTO account (username, salt, verifier, email, joindate, last_ip)
                    VALUES (?, ?, ?, ?, NOW(), ?)
                ");

                $client_ip = getRealIP();
                $stmt->execute([$username, $salt, $verifier, $email, $client_ip]);
                
                $success = true;
                writeLog('INFO', 'New account created', ['username' => $username, 'email' => $email]);
                
            } catch (Exception $e) {
                $errors[] = 'Erro ao criar conta. Tente novamente.';
                writeLog('ERROR', 'Register creation error: ' . $e->getMessage());
            }
        }
    }
}

// Dados para o template
$page_data = [
    'title' => 'Registrar - ' . SITE_NAME,
    'description' => 'Crie sua conta no ' . SITE_NAME,
    'current_page' => 'register'
];

?>
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= escape($page_data['title']) ?></title>
    <meta name="description" content="<?= escape($page_data['description']) ?>">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?= ASSETS_PATH ?>/css/style.css" rel="stylesheet">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= ASSETS_PATH ?>/images/favicon.ico">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-shield-alt me-2"></i>
                <?= SITE_NAME ?>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Início</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/download">Download</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/ranking">Ranking</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/forum">Fórum</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/donate">Doações</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/login">Login</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/register">Registrar</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

<style>
/* Estilos específicos para página de registro */
.register-container {
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    min-height: 100vh;
    padding-top: 100px;
    color: var(--text-light);
}

.register-card {
    background: rgba(26, 26, 46, 0.95);
    border: 2px solid var(--primary-color);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.5);
    backdrop-filter: blur(10px);
}

.register-header {
    background: linear-gradient(135deg, var(--primary-color), #e6b800);
    color: var(--secondary-color);
    border-radius: 13px 13px 0 0;
    padding: 20px;
    text-align: center;
    font-weight: bold;
}

.form-control {
    background: rgba(255,255,255,0.1);
    border: 1px solid var(--primary-color);
    color: var(--text-light);
    border-radius: 8px;
}

.form-control:focus {
    background: rgba(255,255,255,0.15);
    border-color: var(--primary-color);
    box-shadow: 0 0 10px rgba(244, 196, 48, 0.3);
    color: var(--text-light);
}

.form-control::placeholder {
    color: rgba(255,255,255,0.6);
}

.btn-register {
    background: linear-gradient(135deg, var(--primary-color), #e6b800);
    border: none;
    color: var(--secondary-color);
    font-weight: bold;
    padding: 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-register:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(244, 196, 48, 0.4);
    color: var(--secondary-color);
}

.alert-success {
    background: rgba(40, 167, 69, 0.2);
    border: 1px solid var(--success-color);
    color: var(--success-color);
}

.alert-danger {
    background: rgba(220, 53, 69, 0.2);
    border: 1px solid var(--danger-color);
    color: var(--danger-color);
}

.form-label {
    color: var(--primary-color);
    font-weight: 500;
}

.login-link {
    color: var(--primary-color);
    text-decoration: none;
}

.login-link:hover {
    color: #e6b800;
    text-decoration: underline;
}
</style>

<div class="register-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="register-card">
                    <div class="register-header">
                        <h4><i class="fas fa-user-plus me-2"></i>Criar Conta</h4>
                        <p class="mb-0">Junte-se à aventura em Azeroth</p>
                    </div>
                    <div class="p-4">
                        <?php if ($success): ?>
                            <div class="alert alert-success text-center">
                                <i class="fas fa-check-circle me-2"></i>
                                <strong>Conta criada com sucesso!</strong><br>
                                Você já pode fazer login e começar sua jornada.
                            </div>
                            <div class="text-center">
                                <a href="/login" class="btn btn-register w-100">
                                    <i class="fas fa-sign-in-alt me-2"></i>Fazer Login
                                </a>
                            </div>
                        <?php else: ?>
                            <?php if (!empty($errors)): ?>
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <ul class="mb-0">
                                        <?php foreach ($errors as $error): ?>
                                            <li><?= escape($error) ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <form method="POST" id="registerForm">
                                <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">

                                <div class="mb-3">
                                    <label for="username" class="form-label">
                                        <i class="fas fa-user me-2"></i>Nome de Usuário
                                    </label>
                                    <input type="text" class="form-control" id="username" name="username"
                                           placeholder="Digite seu nome de usuário"
                                           value="<?= escape($_POST['username'] ?? '') ?>" required>
                                    <small class="text-muted">3-16 caracteres, apenas letras, números e underscore</small>
                                </div>

                                <div class="mb-3">
                                    <label for="email" class="form-label">
                                        <i class="fas fa-envelope me-2"></i>Email
                                    </label>
                                    <input type="email" class="form-control" id="email" name="email"
                                           placeholder="Digite seu email"
                                           value="<?= escape($_POST['email'] ?? '') ?>" required>
                                </div>

                                <div class="mb-3">
                                    <label for="password" class="form-label">
                                        <i class="fas fa-lock me-2"></i>Senha
                                    </label>
                                    <input type="password" class="form-control" id="password" name="password"
                                           placeholder="Digite sua senha" required>
                                    <small class="text-muted">Mínimo 6 caracteres</small>
                                </div>

                                <div class="mb-4">
                                    <label for="confirm_password" class="form-label">
                                        <i class="fas fa-lock me-2"></i>Confirmar Senha
                                    </label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password"
                                           placeholder="Confirme sua senha" required>
                                </div>

                                <div class="d-grid mb-3">
                                    <button type="submit" class="btn btn-register">
                                        <i class="fas fa-user-plus me-2"></i>Criar Conta
                                    </button>
                                </div>
                            </form>

                            <div class="text-center">
                                <p class="mb-0">Já tem uma conta?
                                    <a href="/login" class="login-link">
                                        <i class="fas fa-sign-in-alt me-1"></i>Faça login aqui
                                    </a>
                                </p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><?= SITE_NAME ?></h5>
                    <p class="mb-0">© <?= date('Y') ?> Todos os direitos reservados.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">World of Warcraft é uma marca registrada da Blizzard Entertainment.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
// Validação do formulário
document.addEventListener('DOMContentLoaded', function() {
    const confirmPassword = document.getElementById('confirm_password');
    if (confirmPassword) {
        confirmPassword.addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPasswordValue = this.value;

            if (password !== confirmPasswordValue) {
                this.setCustomValidity('Senhas não coincidem');
            } else {
                this.setCustomValidity('');
            }
        });
    }
});
</script>
</body>
</html>

<?php
/**
 * Calcular SRP6 Verifier para AzerothCore
 * Implementação compatível com AzerothCore/TrinityCore
 */
function calculateSRP6Verifier($username, $password, $salt) {
    // Constantes SRP6 para WoW (padrão RFC 5054)
    $g = gmp_init(7);
    $N = gmp_init('894B645E89E1535BBDAD5B8B290650530801B18EBFBF5E8FAB3C82872A3E9BB7', 16);

    $username = strtoupper($username);
    $password = strtoupper($password);

    // Calcular hash da senha (H(username:password))
    $h1 = sha1($username . ':' . $password, true);

    // Combinar salt + h1 e calcular hash
    $h2 = sha1($salt . $h1, true);

    // Reverter bytes para little-endian (formato WoW)
    $h2_reversed = strrev($h2);

    // Converter para número grande
    $x = gmp_init('0x' . bin2hex($h2_reversed));

    // Calcular verifier: v = g^x mod N
    $v = gmp_powm($g, $x, $N);

    // Converter de volta para 32 bytes em little-endian
    $verifier_hex = gmp_strval($v, 16);

    // Garantir que tem tamanho par
    if (strlen($verifier_hex) % 2 != 0) {
        $verifier_hex = '0' . $verifier_hex;
    }

    // Converter para binário
    $verifier = hex2bin($verifier_hex);

    // Reverter para little-endian
    $verifier = strrev($verifier);

    // Garantir 32 bytes
    return str_pad($verifier, 32, "\0", STR_PAD_RIGHT);
}
?>
