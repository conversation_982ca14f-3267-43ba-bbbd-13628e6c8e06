<?php
/**
 * Página de Ranking
 * 
 * <AUTHOR> Agent
 * @version 1.0
 */

require_once '../includes/config/config.php';

// Parâmetros de paginação e filtros
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 50;
$offset = ($page - 1) * $limit;

$class_filter = intval($_GET['class'] ?? 0);
$race_filter = intval($_GET['race'] ?? 0);
$level_min = max(1, intval($_GET['level_min'] ?? 1));
$level_max = min(80, intval($_GET['level_max'] ?? 80));

// Obter ranking de jogadores
function getRankingPlayers($offset, $limit, $class_filter = 0, $race_filter = 0, $level_min = 1, $level_max = 80) {
    try {
        $char_db = DatabaseManager::getConnection('characters');
        
        $where_conditions = ["level >= ? AND level <= ?"];
        $params = [$level_min, $level_max];
        
        if ($class_filter > 0) {
            $where_conditions[] = "class = ?";
            $params[] = $class_filter;
        }
        
        if ($race_filter > 0) {
            $where_conditions[] = "race = ?";
            $params[] = $race_filter;
        }
        
        $where_clause = implode(' AND ', $where_conditions);
        
        $stmt = $char_db->prepare("
            SELECT name, level, race, class, totaltime, money, 
                   DATE_FORMAT(logout_time, '%d/%m/%Y') as last_seen
            FROM characters
            WHERE $where_clause
            ORDER BY level DESC, totaltime DESC
            LIMIT ? OFFSET ?
        ");
        
        $params[] = $limit;
        $params[] = $offset;
        $stmt->execute($params);
        
        return $stmt->fetchAll();
        
    } catch (Exception $e) {
        writeLog('ERROR', 'Ranking query error: ' . $e->getMessage());
        return [];
    }
}

// Obter total de jogadores para paginação
function getTotalPlayers($class_filter = 0, $race_filter = 0, $level_min = 1, $level_max = 80) {
    try {
        $char_db = DatabaseManager::getConnection('characters');
        
        $where_conditions = ["level >= ? AND level <= ?"];
        $params = [$level_min, $level_max];
        
        if ($class_filter > 0) {
            $where_conditions[] = "class = ?";
            $params[] = $class_filter;
        }
        
        if ($race_filter > 0) {
            $where_conditions[] = "race = ?";
            $params[] = $race_filter;
        }
        
        $where_clause = implode(' AND ', $where_conditions);
        
        $stmt = $char_db->prepare("SELECT COUNT(*) as total FROM characters WHERE $where_clause");
        $stmt->execute($params);
        
        return $stmt->fetch()['total'];
        
    } catch (Exception $e) {
        return 0;
    }
}

$players = getRankingPlayers($offset, $limit, $class_filter, $race_filter, $level_min, $level_max);
$total_players = getTotalPlayers($class_filter, $race_filter, $level_min, $level_max);
$total_pages = ceil($total_players / $limit);

// Dados para o template
$page_data = [
    'title' => 'Ranking - ' . SITE_NAME,
    'description' => 'Ranking dos melhores jogadores do ' . SITE_NAME,
    'current_page' => 'ranking'
];

?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= escape($page_data['title']) ?></title>
    <meta name="description" content="<?= escape($page_data['description']) ?>">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?= ASSETS_PATH ?>/css/style.css" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= ASSETS_PATH ?>/images/favicon.ico">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-shield-alt me-2"></i>
                <?= SITE_NAME ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Início</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/download">Download</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/ranking">Ranking</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/forum">Fórum</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/donate">Doações</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> <?= escape($_SESSION['username']) ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="/account">Minha Conta</a></li>
                                <li><a class="dropdown-item" href="/characters">Personagens</a></li>
                                <?php if (isAdmin()): ?>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="/admin">Painel Admin</a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="/logout">Sair</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="/login">Login</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/register">Registrar</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-5 pt-5">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-trophy text-warning"></i> Ranking de Jogadores
                </h1>
                
                <!-- Filtros -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" action="/ranking" class="row g-3">
                            <div class="col-md-3">
                                <label for="class" class="form-label">Classe</label>
                                <select class="form-select" id="class" name="class">
                                    <option value="0">Todas as Classes</option>
                                    <option value="1" <?= $class_filter == 1 ? 'selected' : '' ?>>Guerreiro</option>
                                    <option value="2" <?= $class_filter == 2 ? 'selected' : '' ?>>Paladino</option>
                                    <option value="3" <?= $class_filter == 3 ? 'selected' : '' ?>>Caçador</option>
                                    <option value="4" <?= $class_filter == 4 ? 'selected' : '' ?>>Ladino</option>
                                    <option value="5" <?= $class_filter == 5 ? 'selected' : '' ?>>Sacerdote</option>
                                    <option value="6" <?= $class_filter == 6 ? 'selected' : '' ?>>Cavaleiro da Morte</option>
                                    <option value="7" <?= $class_filter == 7 ? 'selected' : '' ?>>Xamã</option>
                                    <option value="8" <?= $class_filter == 8 ? 'selected' : '' ?>>Mago</option>
                                    <option value="9" <?= $class_filter == 9 ? 'selected' : '' ?>>Bruxo</option>
                                    <option value="11" <?= $class_filter == 11 ? 'selected' : '' ?>>Druida</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="level_min" class="form-label">Level Min</label>
                                <input type="number" class="form-control" id="level_min" name="level_min" 
                                       value="<?= $level_min ?>" min="1" max="80">
                            </div>
                            <div class="col-md-2">
                                <label for="level_max" class="form-label">Level Max</label>
                                <input type="number" class="form-control" id="level_max" name="level_max" 
                                       value="<?= $level_max ?>" min="1" max="80">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> Filtrar
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <a href="/ranking" class="btn btn-outline-secondary">
                                        <i class="fas fa-times"></i> Limpar
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Ranking -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list"></i> 
                            Ranking (<?= number_format($total_players) ?> jogadores)
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($players)): ?>
                            <div class="text-center p-4">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <p class="text-muted">Nenhum jogador encontrado com os filtros selecionados.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Rank</th>
                                            <th>Nome</th>
                                            <th>Level</th>
                                            <th>Classe</th>
                                            <th>Raça</th>
                                            <th>Tempo Jogado</th>
                                            <th>Último Login</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($players as $index => $player): ?>
                                            <?php $rank = $offset + $index + 1; ?>
                                            <tr>
                                                <td>
                                                    <span class="rank-badge rank-<?= min($rank, 3) ?>">
                                                        #<?= $rank ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <strong style="color: <?= getClassColor($player['class']) ?>">
                                                        <?= escape($player['name']) ?>
                                                    </strong>
                                                </td>
                                                <td>
                                                    <span class="badge bg-primary"><?= $player['level'] ?></span>
                                                </td>
                                                <td><?= getClassName($player['class']) ?></td>
                                                <td>
                                                    <span class="faction-<?= getFactionIcon($player['race']) ?>">
                                                        <?= getRaceName($player['race']) ?>
                                                    </span>
                                                </td>
                                                <td><?= formatPlayTime($player['totaltime']) ?></td>
                                                <td><?= $player['last_seen'] ?: 'Nunca' ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Paginação -->
                <?php if ($total_pages > 1): ?>
                    <nav aria-label="Paginação do ranking" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?= $page - 1 ?>&class=<?= $class_filter ?>&level_min=<?= $level_min ?>&level_max=<?= $level_max ?>">
                                        Anterior
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                                    <a class="page-link" href="?page=<?= $i ?>&class=<?= $class_filter ?>&level_min=<?= $level_min ?>&level_max=<?= $level_max ?>">
                                        <?= $i ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($page < $total_pages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?= $page + 1 ?>&class=<?= $class_filter ?>&level_min=<?= $level_min ?>&level_max=<?= $level_max ?>">
                                        Próximo
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
